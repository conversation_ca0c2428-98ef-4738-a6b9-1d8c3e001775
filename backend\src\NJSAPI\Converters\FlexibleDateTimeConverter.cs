using System;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace NJSAPI.Converters
{
    public class FlexibleDateTimeConverter : JsonConverter<DateTime?>
    {
        private readonly string[] _dateFormats = new[]
        {
            "yyyy-MM-dd",
            "yyyy-MM-ddTHH:mm:ss",
            "yyyy-MM-ddTHH:mm:ss.fff",
            "yyyy-MM-ddTHH:mm:ss.fffZ",
            "yyyy-MM-ddTHH:mm:ssZ",
            "MM/dd/yyyy",
            "dd/MM/yyyy",
            "M/d/yyyy",
            "d/M/yyyy",
            "M-d-yyyy",
            "d-M-yyyy",
            "MM-dd-yyyy",
            "dd-MM-yyyy"
        };

        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                return null;
            }

            if (reader.TokenType == JsonTokenType.String)
            {
                var dateString = reader.GetString();
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }

                // Try parsing with various formats
                foreach (var format in _dateFormats)
                {
                    if (DateTime.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var date))
                    {
                        return date;
                    }
                }

                // Try general parsing as fallback
                if (DateTime.TryParse(dateString, CultureInfo.InvariantCulture, DateTimeStyles.None, out var generalDate))
                {
                    return generalDate;
                }

                // If all parsing fails, throw an exception with helpful message
                throw new JsonException($"Unable to parse date string '{dateString}'. Expected formats: {string.Join(", ", _dateFormats)}");
            }

            throw new JsonException($"Unexpected token type {reader.TokenType} when parsing DateTime");
        }

        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
            {
                writer.WriteStringValue(value.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }

    public class FlexibleDateTimeConverterNonNullable : JsonConverter<DateTime>
    {
        private readonly string[] _dateFormats = new[]
        {
            "yyyy-MM-dd",
            "yyyy-MM-ddTHH:mm:ss",
            "yyyy-MM-ddTHH:mm:ss.fff",
            "yyyy-MM-ddTHH:mm:ss.fffZ",
            "yyyy-MM-ddTHH:mm:ssZ",
            "MM/dd/yyyy",
            "dd/MM/yyyy",
            "M/d/yyyy",
            "d/M/yyyy",
            "M-d-yyyy",
            "d-M-yyyy",
            "MM-dd-yyyy",
            "dd-MM-yyyy"
        };

        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                var dateString = reader.GetString();
                if (string.IsNullOrEmpty(dateString))
                {
                    throw new JsonException("Date string cannot be null or empty for non-nullable DateTime");
                }

                // Try parsing with various formats
                foreach (var format in _dateFormats)
                {
                    if (DateTime.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var date))
                    {
                        return date;
                    }
                }

                // Try general parsing as fallback
                if (DateTime.TryParse(dateString, CultureInfo.InvariantCulture, DateTimeStyles.None, out var generalDate))
                {
                    return generalDate;
                }

                // If all parsing fails, throw an exception with helpful message
                throw new JsonException($"Unable to parse date string '{dateString}'. Expected formats: {string.Join(", ", _dateFormats)}");
            }

            throw new JsonException($"Unexpected token type {reader.TokenType} when parsing DateTime");
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
        }
    }
}
