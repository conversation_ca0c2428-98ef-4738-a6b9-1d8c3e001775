# Test script to verify WBS API endpoints work correctly
$baseUrl = "http://localhost:5245/api"

Write-Host "Testing WBS API endpoints..." -ForegroundColor Green

# Test 1: GET /api/projects/2/WBS
Write-Host ""
Write-Host "1. Testing GET /api/projects/2/WBS" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/projects/2/WBS" -Method GET -TimeoutSec 30
    Write-Host "✓ GET /api/projects/2/WBS - SUCCESS (Status: 200 OK)" -ForegroundColor Green
    Write-Host "Response contains $($response.Count) items" -ForegroundColor Cyan
} catch {
    Write-Host "✗ GET /api/projects/2/WBS - FAILED" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# Test 2: GET /api/projects/2/WBS/tasks/6
Write-Host ""
Write-Host "2. Testing GET /api/projects/2/WBS/tasks/6" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/projects/2/WBS/tasks/6" -Method GET -TimeoutSec 30
    Write-Host "✓ GET /api/projects/2/WBS/tasks/6 - SUCCESS (Status: 200 OK)" -ForegroundColor Green
    Write-Host "Task Name: $($response.taskName)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ GET /api/projects/2/WBS/tasks/6 - FAILED" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# Test 3: POST /api/projects/2/WBS/tasks with date parsing
Write-Host ""
Write-Host "3. Testing POST /api/projects/2/WBS/tasks with date parsing" -ForegroundColor Yellow
$testPayload = @{
    taskName = "Test Date Parsing Task"
    level = 1
    taskType = "Manpower"
    plannedHours = @(
        @{
            date = "1-7-2025"
            plannedHours = 8
            unit = "day"
        }
    )
} | ConvertTo-Json -Depth 3

Write-Host "Payload: $testPayload" -ForegroundColor Gray

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/projects/2/WBS/tasks" -Method POST -Body $testPayload -ContentType "application/json" -TimeoutSec 30
    Write-Host "✓ POST /api/projects/2/WBS/tasks - SUCCESS (Status: 200 OK)" -ForegroundColor Green
    Write-Host "Created Task ID: $($response.id)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ POST /api/projects/2/WBS/tasks - FAILED" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "API testing completed!" -ForegroundColor Green
