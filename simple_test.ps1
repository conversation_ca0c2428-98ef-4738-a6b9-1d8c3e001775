Write-Host "Testing API endpoints..."

# Test 1: GET WBS
Write-Host "Test 1: GET /api/projects/2/WBS"
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5245/api/projects/2/WBS" -Method GET
    Write-Host "SUCCESS - Status: 200 OK"
    Write-Host "Response count: $($response.Count)"
} catch {
    Write-Host "FAILED - Error: $($_.Exception.Message)"
}

Write-Host ""

# Test 2: GET specific task
Write-Host "Test 2: GET /api/projects/2/WBS/tasks/6"
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5245/api/projects/2/WBS/tasks/6" -Method GET
    Write-Host "SUCCESS - Status: 200 OK"
    Write-Host "Task name: $($response.taskName)"
} catch {
    Write-Host "FAILED - Error: $($_.Exception.Message)"
}

Write-Host ""

# Test 3: POST with date parsing
Write-Host "Test 3: POST /api/projects/2/WBS/tasks with date parsing"
$payload = '{"taskName":"Test Task","level":1,"taskType":"Manpower","plannedHours":[{"date":"1-7-2025","plannedHours":8,"unit":"day"}]}'
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5245/api/projects/2/WBS/tasks" -Method POST -Body $payload -ContentType "application/json"
    Write-Host "SUCCESS - Status: 200 OK"
    Write-Host "Created task ID: $($response.id)"
} catch {
    Write-Host "FAILED - Error: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "Testing completed!"
