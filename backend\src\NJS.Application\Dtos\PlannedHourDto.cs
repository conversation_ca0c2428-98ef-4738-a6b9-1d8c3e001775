using System.Text.Json.Serialization;
using System.Globalization;

namespace NJS.Application.Dtos
{
    public class PlannedHourDto
    {
        public int Year { get; set; }
        public int MonthNo { get; set; } // Month number (1-12) - changed from string to int

        private DateTime? _date;

        [JsonPropertyName("date")]
        public string DateString
        {
            get => _date?.ToString("d-M-yyyy");
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    _date = null;
                    return;
                }

                // Try to parse various date formats
                var formats = new[] { "d-M-yyyy", "dd-MM-yyyy", "M-d-yyyy", "MM-dd-yyyy", "yyyy-MM-dd", "d/M/yyyy", "dd/MM/yyyy" };

                foreach (var format in formats)
                {
                    if (DateTime.TryParseExact(value, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                    {
                        _date = parsedDate;
                        return;
                    }
                }

                // Try general parsing as fallback
                if (DateTime.TryParse(value, out var generalDate))
                {
                    _date = generalDate;
                }
                else
                {
                    _date = null;
                }
            }
        }

        [JsonIgnore]
        public DateTime? Date
        {
            get => _date;
            set => _date = value;
        }

        public int? WeekNo { get; set; } // Week number (1-53) - optional for weekly planning
        public double PlannedHours { get; set; }
        // public double? ActualHours { get; set; } // Future enhancement
    }
}
