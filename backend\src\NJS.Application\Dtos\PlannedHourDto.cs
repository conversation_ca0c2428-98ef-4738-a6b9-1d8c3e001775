namespace NJS.Application.Dtos
{
    public class PlannedHourDto
    {
        public int Year { get; set; }
        public int MonthNo { get; set; } // Month number (1-12) - changed from string to int
        public DateTime? Date { get; set; } // Full date - optional for daily planning
        public int? WeekNo { get; set; } // Week number (1-53) - optional for weekly planning
        public double PlannedHours { get; set; }
        // public double? ActualHours { get; set; } // Future enhancement
    }
}
